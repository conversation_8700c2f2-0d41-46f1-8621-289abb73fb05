#ConfigMap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ape-crm-dev-config
  namespace: ape-crm-dev
data:
  NODE_ENV: 'production'
  PORT: '80'
  TZ: 'UTC'
  REQUEST_TIMEOUT: '180000'
  #Swagger Config
  SWAGGER_TITLE: 'APE CRM API'
  SWAGGER_DESCRIPTION: 'The APE CRM API'
  SWAGGER_VERSION: '1.0'
  # Primary Database
  DB_PRIMARY_TYPE: 'postgres'
  DB_PRIMARY_HOST: 'ape-postgre.c7uzjfmteanl.ap-southeast-1.rds.amazonaws.com'
  DB_PRIMARY_PORT: '5432'
  DB_PRIMARY_USERNAME: 'ape-crm'
  DB_PRIMARY_PASSWORD: 'Ac#23pehA2in'
  DB_PRIMARY_DATABASE: 'ape-crm-dev'
  DB_PRIMARY_SYNCHRONIZE: 'true'
  DB_PRIMARY_SSL: 'true'
  DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: 'false'
  # JWT HS256 config
  JWT_SECRET: '/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo: '
  JWT_EXPIRY: '100d'
  JWT_REFRESH_TOKEN_SECRET: '/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi: '
  JWT_REFRESH_TOKEN_EXPIRY: '300d'
  # APE SSO
  APE_SSO_URL: 'http://localhost:4501'
  APE_CLIENT_ID: 'ape_b808f7c5-8b29-47e0-bf3f-b9c5a7fefd61_CRM_225a43df'
  APE_CLIENT_SECRET: '82f713e84a0c9632c57b44ccd7d0d03ddab78d08d50f5b8dbea070eb639f4611'
  APE_CALLBACK_URL: 'http://localhost:4001/api/client/auth/ape/callback'

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ape-crm-dev
  namespace: ape-crm-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ape-crm-dev
  template:
    metadata:
      labels:
        app: ape-crm-dev
    spec:
      containers:
        - name: ape-crm-dev
          image: 077293829360.dkr.ecr.ap-southeast-1.amazonaws.com/ape-crm-dev:latest
          ports:
            - containerPort: 80
          envFrom:
            - configMapRef:
                name: ape-crm-dev-config
          volumeMounts:
            - mountPath: /etc/localtime
              name: tz-config
      volumes:
        - name: tz-config
          hostPath:
            path: /usr/share/zoneinfo/Asia/Ho_Chi_Minh

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: ape-crm-dev
  namespace: ape-crm-dev
  labels:
    run: ape-crm-dev
spec:
  type: ClusterIP
  ports:
    - port: 80
      protocol: TCP
      targetPort: 80
  selector:
    app: ape-crm-dev
