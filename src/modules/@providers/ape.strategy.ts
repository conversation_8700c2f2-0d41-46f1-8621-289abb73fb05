// auth/strategies/google.strategy.ts

import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import * as express from 'express';
import { Strategy, VerifyCallback } from 'passport-oauth2';
import { optionalApiConnector } from '~/connectors';
import { configEnv } from '~/@config/env';

const { APE_SSO_URL, APE_CLIENT_ID, APE_CLIENT_SECRET, APE_CALLBACK_URL } = configEnv();

@Injectable()
export class ApeStrategy extends PassportStrategy(Strategy, 'ape-sso') {
  constructor() {
    super({
      authorizationURL: APE_SSO_URL + '/api/client/auth/authorize',
      tokenURL: APE_SSO_URL + '/api/client/auth/token',
      clientID: APE_CLIENT_ID,
      clientSecret: APE_CLIENT_SECRET,
      callbackURL: APE_CALLBACK_URL,
      scope: ['email', 'profile'],
      passReqToCallback: true,
    });
  }

  async userProfile(accessToken: string, done: (err?: unknown, profile?: any) => void) {
    try {
      const profile = await optionalApiConnector.get(
        APE_SSO_URL + '/api/client/auth/userinfo',
        {},
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );
      done(null, profile);
    } catch (err) {
      done(err);
    }
  }

  async validate(
    req: express.Request, // 👈 có query.state ở đây
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ) {
    console.log('PARAMS:', { accessToken, refreshToken, profile, done });

    const { id, username, fullName, avatar } = profile;

    const redirectUri = decodeURIComponent((req.query.state as string) || '');
    console.log('redirectUri in validate:', redirectUri);

    const user = {
      providerId: id,
      username,
      fullName,
      avatar,
      redirectUri, // 👈 gắn lại cho req.user
    };

    done(null, user);
  }
}
