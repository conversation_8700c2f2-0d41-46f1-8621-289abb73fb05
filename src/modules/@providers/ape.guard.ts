import { AuthGuard } from '@nestjs/passport';
import { ExecutionContext, Injectable } from '@nestjs/common';

@Injectable()
export class ApeAuthGuard extends AuthGuard('ape-sso') {
  canActivate(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest();
    console.log(`=====APE AUTH GUARD=====`, req.query);
    const redirectUri = req.query.redirectUri;
    if (redirectUri) {
      req.session = req.session || {};
      req.session.redirectUri = redirectUri;
    }

    return super.canActivate(context);
  }

  getAuthenticateOptions(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest();
    return {
      state: req.session?.redirectUri || '',
    };
  }
}
