import { MiddlewareConsumer, NestModule } from '@nestjs/common';
import { ChildModule } from 'nestjs-typeorm3-kit';
import { REFIX_MODULE } from '../config-module';
import { MemberAuthService } from './member-auth/member-auth.service';
import { MemberAuthController } from './member-auth/member-auth.controller';
import { PrimaryRepoModule } from '~/domains/primary/primary-repo.module';
import { PassportModule } from '@nestjs/passport';
import { ApeStrategy } from '../@providers/ape.strategy';
@ChildModule({
  prefix: REFIX_MODULE.client,
  imports: [PrimaryRepoModule, PassportModule],
  providers: [ApeStrategy, MemberAuthService],
  controllers: [MemberAuthController],
})
export class ClientModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {}
}
