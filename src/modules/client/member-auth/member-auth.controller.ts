import { MemberAuthService } from './member-auth.service';
import { Body, Query, Req, Res } from '@nestjs/common';
import { MemberLoginReq, MemberRegisterReq, MemberUpdateInfoDto } from './dto';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import * as express from 'express';
import * as querystring from 'querystring';
import { BusinessException } from '~/@systems/exceptions';
import { DefController, DefGet } from 'nestjs-typeorm3-kit';
import { ApeAuthGuard } from '~/modules/@providers/ape.guard';

@DefController('auth')
export class MemberAuthController {
  constructor(private readonly memberAuthService: MemberAuthService) {}

  /**
   * @vn Lấy thông tin profile user
   * @en Get profile user
   */
  //   @UseGuards(JwtAuthGuard)
  @DefGet('me', {
    summary: 'Get profile user',
  })
  me(@Req() req: express.Request & { user: { id: string } }) {
    // lấy thông tin user từ DB
  }

  @UseGuards(ApeAuthGuard)
  @DefGet('ape')
  apeLogin(@Req() req: express.Request, @Res() res: express.Response) {
    const redirectUri = req.query.redirectUri as string;

    if (redirectUri) {
      res.cookie('redirectUri', redirectUri, {
        httpOnly: true,
        maxAge: 5 * 60 * 1000,
        sameSite: 'lax', // hoặc 'none' nếu FE khác domain
        secure: false, // ✅ nếu bạn dùng HTTPS thì set true
      });
    }
  }

  @DefGet('ape/callback')
  @UseGuards(ApeAuthGuard)
  async apeAuthRedirect(@Req() req, @Res() res: express.Response) {
    console.log(`=====APE CALLBACK=====`, req.user);
    const result = await this.memberAuthService.apeLogin(req.user);
    const redirectUri = req.user.redirectUri;
    const query = querystring.stringify(result);
    const finalRedirect = `${redirectUri}?${query}`;
    return res.redirect(finalRedirect);
  }
}
