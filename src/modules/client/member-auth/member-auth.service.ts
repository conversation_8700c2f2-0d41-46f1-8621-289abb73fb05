import { Injectable } from '@nestjs/common';
import { MemberLoginReq, MemberRegisterReq, MemberUpdateInfoDto } from './dto';
import { MemberRepo } from '~/domains/primary';
import { BusinessException } from '~/@systems/exceptions';
import securityHelper from '~/@core/helpers/security.helper';
import { MemberEntity } from '~/domains/primary';
import { JwtService } from '@nestjs/jwt';
import { configEnv } from '~/@config/env';
import { NSMember } from '~/common/enums';
import { MemberAuthProviderRepo } from '~/domains/primary';
import { InjectRepo } from 'nestjs-typeorm3-kit';
@Injectable()
export class MemberAuthService {
  constructor(
    private jwtService: JwtService,
    @InjectRepo(MemberRepo) private memberRepo: MemberRepo,
    @InjectRepo(MemberAuthProviderRepo) private memberAuthProviderRepo: MemberAuthProviderRepo,
  ) {}

  private async generateRefreshToken(memberId: string) {
    const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
    const newRefreshToken = await this.jwtService.signAsync(
      { sub: memberId },
      {
        secret: JWT_REFRESH_TOKEN_SECRET,
        expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
      },
    );

    return newRefreshToken;
  }

  private clearPrivateMemberData(member: MemberEntity) {
    const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = member;
    return rest;
  }

  private async makeAuthResponse(member: MemberEntity) {
    const pipeMember = this.clearPrivateMemberData(member);
    const payload = {
      sub: member.id,
      ...pipeMember,
    };
    return {
      accessToken: await this.jwtService.signAsync(payload),
      refreshToken: await this.generateRefreshToken(member.id),
      tokenType: 'Bearer',
      ...pipeMember,
    };
  }

  async apeLogin(profile: {
    username: string;
    providerId: string;
    fullName?: string;
    avatar?: string;
  }) {
    const { username, providerId, fullName, avatar } = profile;
    let member = await this.memberRepo.findOne({
      where: {
        username,
      },
    });
    if (!member) {
      member = await this.memberRepo.save({
        username,
        fullName,
        avatar,
      });
      await this.memberAuthProviderRepo.save({
        memberId: member.id,
        provider: NSMember.EAuthProviderType.APE,
        providerId,
      });
      return this.makeAuthResponse(member);
    }
    if (member.status === NSMember.EStatus.LOCKED) {
      throw new BusinessException('member_auth.login.error.locked');
    }
    // Tìm xem đã liên kết với provider chưa
    const provider = await this.memberAuthProviderRepo.findOne({
      where: {
        provider: NSMember.EAuthProviderType.APE,
        providerId,
      },
    });
    if (!provider) {
      await this.memberAuthProviderRepo.save({
        memberId: member.id,
        provider: NSMember.EAuthProviderType.APE,
        providerId,
      });
      return this.makeAuthResponse(member);
    }
    return this.makeAuthResponse(member);
  }
}
