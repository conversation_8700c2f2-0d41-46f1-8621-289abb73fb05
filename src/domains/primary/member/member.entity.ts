import { Entity, Column } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';
import { NSMember } from '~/common/enums';

@Entity('member')
export class MemberEntity extends PrimaryBaseEntity {
  @ApiProperty({ example: '<EMAIL>' })
  @Column({ nullable: true, unique: true, type: 'varchar', length: 255 })
  username: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 255 })
  password?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 255 })
  fullName?: string;

  @ApiPropertyOptional()
  @Column({ nullable: true, type: 'varchar', length: 255 })
  avatar?: string;

  @ApiProperty()
  @Column({ type: 'varchar', default: NSMember.EStatus.INACTIVE })
  status: NSMember.EStatus;
}
